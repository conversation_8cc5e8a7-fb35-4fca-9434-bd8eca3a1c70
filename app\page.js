import Image from "next/image";
import AppointmentForm from "../components/AppointmentForm";

export default function Home() {
  return (
    <div className="min-h-screen bg-[var(--light-background)]">
      {/* Header */}
      <header className="bg-[var(--secondary-blue)] text-white py-6 px-4">
        <div className="max-w-4xl mx-auto flex flex-col sm:flex-row items-center justify-between">
          <div className="flex items-center space-x-4 mb-4 sm:mb-0">
            <Image
              src="/img/logo/logo.png"
              alt="CAF Logo"
              width={60}
              height={60}
              className="rounded-lg"
            />
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">Centro di Assistenza Fiscale Montesacro</h1>
              <p className="text-blue-200">Prenotazione Appuntamenti</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Information Section */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold text-[var(--primary-red)] mb-4">
                Prenota il tuo Appuntamento
              </h2>
              <p className="text-[var(--secondary-text)] mb-4">
                Utilizza il nostro sistema di prenotazione online per accedere più velocemente ai servizi del CAF.
                Compila il modulo con i tuoi dati e scegli il servizio di cui hai bisogno.
              </p>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-[var(--primary-red)] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-[var(--primary-text)]">Compila il modulo</h3>
                    <p className="text-[var(--secondary-text)] text-sm">Inserisci i tuoi dati personali e scegli il servizio</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-[var(--primary-red)] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-[var(--primary-text)]">Seleziona data e orario</h3>
                    <p className="text-[var(--secondary-text)] text-sm">Scegli il giorno e l&apos;ora più comodi per te</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-[var(--primary-red)] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-[var(--primary-text)]">Ricevi la conferma</h3>
                    <p className="text-[var(--secondary-text)] text-sm">Ti invieremo una email di conferma con tutti i dettagli</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Services Info */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-bold text-[var(--primary-red)] mb-4">I Nostri Servizi</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-[var(--primary-red)] rounded-full"></div>
                  <span className="text-[var(--primary-text)]">Servizi Patronato</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-[var(--primary-red)] rounded-full"></div>
                  <span className="text-[var(--primary-text)]">Consulenza Legale</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-[var(--primary-red)] rounded-full"></div>
                  <span className="text-[var(--primary-text)]">Sportello Immigrazione</span>
                </div>
              </div>
            </div>
          </div>

          {/* Appointment Form */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-[var(--primary-text)] mb-6">
              Modulo di Prenotazione
            </h2>
            <AppointmentForm />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[var(--secondary-blue)] text-white py-8 px-4 mt-12">
        <div className="max-w-4xl mx-auto text-center">
          <p className="text-blue-200">
            © 2025 CAF Montesacro. Powered by <strong>Programmarti</strong>.
          </p>
        </div>
      </footer>
    </div>
  );
}
