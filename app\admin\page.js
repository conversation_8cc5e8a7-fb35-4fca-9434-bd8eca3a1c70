'use client';

import { useState, useEffect } from 'react';
import LoginForm from '../../components/LoginForm';

export default function AdminPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');

  // Filter state - moved up from AppointmentsTab to preserve across operations
  const [currentFilters, setCurrentFilters] = useState({
    date: new Date().toISOString().split('T')[0], // Default to today
    search: '',
    status: 'all'
  });

  // Check authentication on component mount
  useEffect(() => {
    const savedToken = localStorage.getItem('adminToken');
    const savedUser = localStorage.getItem('adminUser');

    if (savedToken && savedUser) {
      setToken(savedToken);
      setUser(JSON.parse(savedUser));
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  // Fetch data when authenticated
  useEffect(() => {
    if (isAuthenticated && token) {
      fetchDashboardData();
      fetchAppointments(currentFilters);
    }
  }, [isAuthenticated, token]);

  const handleLogin = (newToken, newUser) => {
    setToken(newToken);
    setUser(newUser);
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
    setAppointments([]);
    setStats(null);
  };

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/appointments?action=stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const fetchAppointments = async (filters = {}) => {
    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (filters.date) {
        params.append('date', filters.date);
      }
      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.status && filters.status !== 'all') {
        params.append('status', filters.status);
      }

      const queryString = params.toString();
      const url = `/api/admin/appointments${queryString ? `?${queryString}` : ''}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setAppointments(result.data);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    }
  };

  // Handle filter updates from AppointmentsTab
  const handleFilterUpdate = async (newFilters) => {
    setCurrentFilters(newFilters);
    await fetchAppointments(newFilters);
  };

  const updateAppointmentStatus = async (appointmentId, status) => {
    try {
      console.log(`Updating appointment ${appointmentId} to status ${status}`);

      const response = await fetch('/api/admin/appointments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ appointmentId, status })
      });

      const result = await response.json();

      if (response.ok) {
        console.log('Status update successful');
        // Refresh appointments with current filters preserved
        fetchAppointments(currentFilters);
        fetchDashboardData();
      } else {
        console.error('Status update failed:', result);
        alert(`Errore nell'aggiornamento dello stato: ${result.message || 'Errore sconosciuto'}`);
      }
    } catch (error) {
      console.error('Error updating appointment:', error);
      alert('Errore di connessione durante l\'aggiornamento dello stato');
    }
  };

  const deleteAppointment = async (appointmentId) => {
    if (!confirm('Sei sicuro di voler eliminare questo appuntamento?')) {
      return;
    }

    try {
      console.log(`Deleting appointment ${appointmentId}`);

      const response = await fetch(`/api/admin/appointments?id=${appointmentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();

      if (response.ok) {
        console.log('Delete successful');
        // Refresh appointments with current filters preserved
        fetchAppointments(currentFilters);
        fetchDashboardData();
      } else {
        console.error('Delete failed:', result);
        alert(`Errore nell'eliminazione dell'appuntamento: ${result.message || 'Errore sconosciuto'}`);
      }
    } catch (error) {
      console.error('Error deleting appointment:', error);
      alert('Errore di connessione durante l\'eliminazione dell\'appuntamento');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[var(--light-background)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-red)] mx-auto"></div>
          <p className="mt-4 text-[var(--secondary-text)]">Caricamento...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm onLogin={handleLogin} />;
  }

  return (
    <div className="min-h-screen bg-[var(--light-background)]">
      {/* Header */}
      <header className="bg-[var(--secondary-blue)] text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold">Pannello Amministratore CAF</h1>
              <p className="text-blue-200">Benvenuto, {user?.username}</p>
            </div>
            <button
              onClick={handleLogout}
              className="bg-[var(--primary-red)] hover:bg-[var(--hover-accent)] px-4 py-2 rounded-lg transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'dashboard'
                  ? 'border-[var(--primary-red)] text-[var(--primary-red)]'
                  : 'border-transparent text-[var(--secondary-text)] hover:text-[var(--primary-text)]'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('appointments')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'appointments'
                  ? 'border-[var(--primary-red)] text-[var(--primary-red)]'
                  : 'border-transparent text-[var(--secondary-text)] hover:text-[var(--primary-text)]'
              }`}
            >
              Appuntamenti
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {activeTab === 'dashboard' && (
          <DashboardTab stats={stats} />
        )}

        {activeTab === 'appointments' && (
          <AppointmentsTab
            appointments={appointments}
            onUpdateStatus={updateAppointmentStatus}
            onDelete={deleteAppointment}
            onRefreshAppointments={handleFilterUpdate}
            currentFilters={currentFilters}
          />
        )}
      </main>
    </div>
  );
}

// Dashboard Tab Component
function DashboardTab({ stats }) {
  if (!stats) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-red)] mx-auto"></div>
        <p className="mt-2 text-[var(--secondary-text)]">Caricamento statistiche...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">T</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-[var(--secondary-text)]">Totale</p>
              <p className="text-2xl font-bold text-[var(--primary-text)]">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">O</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-[var(--secondary-text)]">Oggi</p>
              <p className="text-2xl font-bold text-[var(--primary-text)]">{stats.today}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">S</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-[var(--secondary-text)]">Questa Settimana</p>
              <p className="text-2xl font-bold text-[var(--primary-text)]">{stats.thisWeek}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">M</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-[var(--secondary-text)]">Questo Mese</p>
              <p className="text-2xl font-bold text-[var(--primary-text)]">{stats.thisMonth}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Services Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-[var(--primary-text)] mb-4">Appuntamenti per Servizio</h3>
          <div className="space-y-3">
            {Object.entries(stats.byService).map(([service, count]) => (
              <div key={service} className="flex items-center justify-between">
                <span className="text-sm text-[var(--secondary-text)]">{service}</span>
                <span className="text-sm font-medium text-[var(--primary-text)]">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Status Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-[var(--primary-text)] mb-4">Stato Appuntamenti</h3>
          <div className="space-y-3">
            {Object.entries(stats.byStatus).map(([status, count]) => (
              <div key={status} className="flex items-center justify-between">
                <span className="text-sm text-[var(--secondary-text)]">
                  {status === 'confirmed' ? 'Confermati' :
                   status === 'completed' ? 'Completati' :
                   status === 'cancelled' ? 'Annullati' : 'Non presentati'}
                </span>
                <span className="text-sm font-medium text-[var(--primary-text)]">{count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Appointments */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-[var(--primary-text)]">Appuntamenti Recenti</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {stats.recent.map((appointment) => (
            <div key={appointment.id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-[var(--primary-text)]">
                    {appointment.nome} {appointment.cognome}
                  </p>
                  <p className="text-sm text-[var(--secondary-text)]">
                    {appointment.servizio} - {appointment.dataAppuntamento} alle {appointment.orario}
                  </p>
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                  appointment.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                  appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {appointment.status === 'confirmed' ? 'Confermato' :
                   appointment.status === 'completed' ? 'Completato' :
                   appointment.status === 'cancelled' ? 'Annullato' : 'Non presentato'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Appointments Tab Component
function AppointmentsTab({ appointments, onUpdateStatus, onDelete, onRefreshAppointments, currentFilters }) {
  const [isLoadingFilter, setIsLoadingFilter] = useState(false);

  // Use filters from parent component
  const filter = currentFilters.status;
  const searchTerm = currentFilters.search;
  const dateFilter = currentFilters.date;

  // Trigger initial load when component mounts (only if no filters are set)
  useEffect(() => {
    if (!dateFilter && !searchTerm && filter === 'all' && onRefreshAppointments) {
      const today = new Date().toISOString().split('T')[0];
      onRefreshAppointments({ date: today, search: '', status: 'all' });
    }
  }, []);

  // Handle date filter change
  const handleDateFilterChange = async (newDate) => {
    setIsLoadingFilter(true);

    try {
      if (onRefreshAppointments) {
        await onRefreshAppointments({
          date: newDate,
          search: searchTerm,
          status: filter
        });
      }
    } finally {
      setIsLoadingFilter(false);
    }
  };

  // Handle search change
  const handleSearchChange = async (newSearch) => {
    setIsLoadingFilter(true);

    try {
      if (onRefreshAppointments) {
        await onRefreshAppointments({
          date: dateFilter,
          search: newSearch,
          status: filter
        });
      }
    } finally {
      setIsLoadingFilter(false);
    }
  };

  // Handle status filter change
  const handleStatusFilterChange = async (newStatus) => {
    setIsLoadingFilter(true);

    try {
      if (onRefreshAppointments) {
        await onRefreshAppointments({
          date: dateFilter,
          search: searchTerm,
          status: newStatus
        });
      }
    } finally {
      setIsLoadingFilter(false);
    }
  };

  // Clear all filters
  const clearAllFilters = async () => {
    setIsLoadingFilter(true);

    try {
      if (onRefreshAppointments) {
        await onRefreshAppointments({
          date: '',
          search: '',
          status: 'all'
        });
      }
    } finally {
      setIsLoadingFilter(false);
    }
  };

  // Set today's filter
  const setTodayFilter = async () => {
    const today = new Date().toISOString().split('T')[0];
    await handleDateFilterChange(today);
  };

  // For backward compatibility, still filter client-side if needed
  const filteredAppointments = appointments;

  // Helper function to check if appointment time has passed (for today's appointments)
  const isAppointmentPast = (appointmentTime, appointmentDate) => {
    if (!appointmentTime || !appointmentDate) return false;

    const today = new Date().toISOString().split('T')[0];
    if (appointmentDate !== today) return false;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    return appointmentTime < currentTime;
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Date Filter */}
          <div>
            <label htmlFor="dateFilter" className="block text-sm font-medium text-[var(--primary-text)] mb-2">
              Data Appuntamento
            </label>
            <input
              type="date"
              id="dateFilter"
              className="form-input"
              value={dateFilter}
              onChange={(e) => handleDateFilterChange(e.target.value)}
              disabled={isLoadingFilter}
            />
            {dateFilter && (
              <p className="text-xs text-[var(--secondary-text)] mt-1">
                {dateFilter === new Date().toISOString().split('T')[0]
                  ? '📅 Oggi'
                  : `📅 ${new Date(dateFilter).toLocaleDateString('it-IT')}`
                }
              </p>
            )}
          </div>

          {/* Search Filter */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-[var(--primary-text)] mb-2">
              Cerca
            </label>
            <input
              type="text"
              id="search"
              className="form-input"
              placeholder="Nome, cognome, email..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              disabled={isLoadingFilter}
            />
          </div>

          {/* Status Filter */}
          <div>
            <label htmlFor="filter" className="block text-sm font-medium text-[var(--primary-text)] mb-2">
              Stato
            </label>
            <select
              id="filter"
              className="form-input"
              value={filter}
              onChange={(e) => handleStatusFilterChange(e.target.value)}
              disabled={isLoadingFilter}
            >
              <option value="all">Tutti</option>
              <option value="confirmed">Confermati</option>
              <option value="completed">Completati</option>
              <option value="cancelled">Annullati</option>
              <option value="no_show">Non presentati</option>
            </select>
          </div>

          {/* Filter Actions */}
          <div>
            <label className="block text-sm font-medium text-[var(--primary-text)] mb-2">
              Azioni
            </label>
            <div className="flex flex-col gap-2">
              <button
                onClick={setTodayFilter}
                className="px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                disabled={isLoadingFilter}
              >
                📅 Oggi
              </button>
              <button
                onClick={clearAllFilters}
                className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                disabled={isLoadingFilter}
              >
                🗑️ Pulisci
              </button>
            </div>
          </div>
        </div>

        {/* Loading indicator */}
        {isLoadingFilter && (
          <div className="mt-4 flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[var(--primary-red)]"></div>
            <span className="ml-2 text-sm text-[var(--secondary-text)]">Aggiornamento filtri...</span>
          </div>
        )}

        {/* Filter summary */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex flex-wrap items-center gap-2 text-sm text-[var(--secondary-text)]">
            <span>Filtri attivi:</span>
            {dateFilter && (
              <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded">
                Data: {dateFilter === new Date().toISOString().split('T')[0] ? 'Oggi' : dateFilter}
              </span>
            )}
            {searchTerm && (
              <span className="px-2 py-1 bg-green-100 text-green-700 rounded">
                Cerca: "{searchTerm}"
              </span>
            )}
            {filter !== 'all' && (
              <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded">
                Stato: {filter === 'confirmed' ? 'Confermati' :
                       filter === 'completed' ? 'Completati' :
                       filter === 'cancelled' ? 'Annullati' : 'Non presentati'}
              </span>
            )}
            {!dateFilter && !searchTerm && filter === 'all' && (
              <span className="text-gray-500">Nessun filtro attivo</span>
            )}
          </div>
        </div>
      </div>

      {/* Appointments Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-[var(--primary-text)]">
              Appuntamenti ({filteredAppointments.length})
            </h3>
            {dateFilter && (
              <div className="text-sm text-[var(--secondary-text)]">
                {dateFilter === new Date().toISOString().split('T')[0] ? (
                  <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full">
                    📅 Appuntamenti di oggi
                  </span>
                ) : (
                  <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full">
                    📅 {new Date(dateFilter).toLocaleDateString('it-IT', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Time-based Summary */}
          {filteredAppointments.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center p-2 bg-yellow-50 rounded">
                <div className="font-semibold text-yellow-700">
                  {filteredAppointments.filter(app => {
                    const hour = parseInt(app.orario?.split(':')[0] || '0');
                    return hour >= 9 && hour <= 13;
                  }).length}
                </div>
                <div className="text-yellow-600">Mattina</div>
              </div>
              <div className="text-center p-2 bg-blue-50 rounded">
                <div className="font-semibold text-blue-700">
                  {filteredAppointments.filter(app => {
                    const hour = parseInt(app.orario?.split(':')[0] || '0');
                    return hour >= 15 && hour <= 18;
                  }).length}
                </div>
                <div className="text-blue-600">Pomeriggio</div>
              </div>
              <div className="text-center p-2 bg-green-50 rounded">
                <div className="font-semibold text-green-700">
                  {filteredAppointments.filter(app => (app.status || 'confirmed') === 'confirmed').length}
                </div>
                <div className="text-green-600">Confermati</div>
              </div>
              <div className="text-center p-2 bg-purple-50 rounded">
                <div className="font-semibold text-purple-700">
                  {filteredAppointments.filter(app => (app.status || 'confirmed') === 'completed').length}
                </div>
                <div className="text-purple-600">Completati</div>
              </div>
            </div>
          )}
        </div>

        {filteredAppointments.length === 0 ? (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="text-6xl mb-4">📅</div>
              <h3 className="text-lg font-medium text-[var(--primary-text)] mb-2">
                Nessun appuntamento trovato
              </h3>
              <p className="text-[var(--secondary-text)] mb-4">
                {dateFilter ? (
                  dateFilter === new Date().toISOString().split('T')[0] ?
                    'Non ci sono appuntamenti programmati per oggi.' :
                    `Non ci sono appuntamenti per il ${new Date(dateFilter).toLocaleDateString('it-IT')}.`
                ) : (
                  'Non ci sono appuntamenti che corrispondono ai filtri selezionati.'
                )}
              </p>
              {dateFilter && (
                <button
                  onClick={clearAllFilters}
                  className="px-4 py-2 bg-[var(--primary-red)] text-white rounded hover:bg-[var(--hover-accent)] transition-colors"
                >
                  Visualizza tutti gli appuntamenti
                </button>
              )}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Orario
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cliente
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contatti
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Servizio
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stato
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Azioni
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAppointments
                  .sort((a, b) => {
                    // Sort by time in ascending order (earliest first)
                    const timeA = a.orario || '00:00';
                    const timeB = b.orario || '00:00';
                    return timeA.localeCompare(timeB);
                  })
                  .map((appointment) => {
                    const isPast = isAppointmentPast(appointment.orario, appointment.dataAppuntamento);
                    const isToday = dateFilter === new Date().toISOString().split('T')[0];

                    return (
                  <tr key={appointment.id} className={`hover:bg-gray-50 transition-colors ${isPast && isToday ? 'opacity-75 bg-gray-25' : ''}`}>
                    {/* Orario Column - First */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`text-lg font-bold ${isPast && isToday ? 'text-gray-500' : 'text-[var(--primary-red)]'}`}>
                          {appointment.orario}
                          {isPast && isToday && (
                            <span className="ml-1 text-xs text-gray-400">⏰</span>
                          )}
                        </div>
                        <div className="ml-2">
                          {/* Time period indicator */}
                          {(() => {
                            const hour = parseInt(appointment.orario?.split(':')[0] || '0');
                            if (hour >= 9 && hour <= 13) {
                              return <span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">Mattina</span>;
                            } else if (hour >= 15 && hour <= 18) {
                              return <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Pomeriggio</span>;
                            }
                            return null;
                          })()}
                        </div>
                      </div>
                    </td>

                    {/* Cliente Column - Second */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-[var(--primary-text)]">
                          {appointment.nome} {appointment.cognome}
                        </div>
                        <div className="text-xs text-[var(--secondary-text)]">
                          ID: {appointment.id}
                        </div>
                      </div>
                    </td>

                    {/* Contatti Column - Third */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-[var(--primary-text)]">{appointment.email}</div>
                      <div className="text-sm text-[var(--secondary-text)]">{appointment.telefono}</div>
                    </td>

                    {/* Servizio Column - Fourth */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-[var(--primary-text)]">{appointment.servizio}</div>
                    </td>

                    {/* Stato Column - Fifth */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={appointment.status || 'confirmed'}
                        onChange={(e) => onUpdateStatus(appointment.id, e.target.value)}
                        className={`text-sm rounded-full px-3 py-1 font-semibold border-0 cursor-pointer transition-colors ${
                          (appointment.status || 'confirmed') === 'confirmed' ? 'bg-green-100 text-green-800 hover:bg-green-200' :
                          (appointment.status || 'confirmed') === 'completed' ? 'bg-blue-100 text-blue-800 hover:bg-blue-200' :
                          (appointment.status || 'confirmed') === 'cancelled' ? 'bg-red-100 text-red-800 hover:bg-red-200' :
                          'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        <option value="confirmed">Confermato</option>
                        <option value="completed">Completato</option>
                        <option value="cancelled">Annullato</option>
                        <option value="no_show">Non presentato</option>
                      </select>
                    </td>

                    {/* Azioni Column - Sixth */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => onDelete(appointment.id)}
                        className="text-red-600 hover:text-red-900 hover:bg-red-50 px-2 py-1 rounded transition-colors"
                        title="Elimina appuntamento"
                      >
                        🗑️ Elimina
                      </button>
                    </td>
                  </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}